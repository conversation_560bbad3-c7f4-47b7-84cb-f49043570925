const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Homepage Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load homepage successfully', async ({ page }) => {
    await expect(page).toHaveTitle(/Ensar Solutions Playbook/);
    await expect(page.locator('h1')).toContainText('Ensar Solutions Playbook');
  });

  test('should have correct meta description', async ({ page }) => {
    const description = await page.locator('meta[name="description"]').getAttribute('content');
    expect(description).toBeTruthy();
  });

  test('should display analytics overview section', async ({ page }) => {
    await expect(page.locator('h2:has-text("Analytics Overview")')).toBeVisible();
  });

  test('should display analytics cards', async ({ page }) => {
    // Check for analytics cards with numbers
    const analyticsCards = page.locator('.analytics-card, .card, [class*="analytic"], [class*="metric"]');
    
    // Wait for at least one analytics element to be visible
    await expect(analyticsCards.first()).toBeVisible({ timeout: 10000 });
    
    // Check if analytics numbers are displayed
    const numbers = page.locator('text=/\\d+/');
    await expect(numbers.first()).toBeVisible();
  });

  test('should have working navigation links', async ({ page }) => {
    // Test main navigation links
    const mainLinks = [
      { text: 'Playbook', url: '/playbook' },
      { text: 'Prompts', url: '/prompts' },
      { text: 'Requirements', url: '/requirements' },
      { text: 'Strategy', url: '/strategy' },
      { text: 'LinkedIn Posts', url: '/linkedin-posts' },
      { text: 'Tech', url: '/tech-checklist' },
      { text: 'Courses', url: '/courses' },
      { text: 'Industry Domains', url: '/industry-domains' },
      { text: 'Applications', url: '/app-info' }
    ];

    for (const link of mainLinks) {
      const linkElement = page.locator(`a:has-text("${link.text}")`).first();
      await expect(linkElement).toBeVisible();
      await expect(linkElement).toHaveAttribute('href', expect.stringContaining(link.url));
    }
  });

  test('should display company branding', async ({ page }) => {
    // Check for Ensar Solutions branding
    await expect(page.locator('text=Ensar Solutions')).toBeVisible();
    
    // Check for logo or company image
    const images = page.locator('img');
    if (await images.count() > 0) {
      await expect(images.first()).toBeVisible();
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone size
    
    // Check that main elements are still visible
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h2:has-text("Analytics Overview")')).toBeVisible();
  });

  test('should load all images successfully', async ({ page }) => {
    const images = page.locator('img');
    const imageCount = await images.count();
    
    if (imageCount > 0) {
      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i);
        await expect(img).toBeVisible();
        
        // Check if image loaded successfully
        const naturalWidth = await img.evaluate(img => img.naturalWidth);
        expect(naturalWidth).toBeGreaterThan(0);
      }
    }
  });

  test('should have accessible heading structure', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toHaveCount(1); // Should have exactly one h1
    
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    expect(await headings.count()).toBeGreaterThan(0);
  });
});
