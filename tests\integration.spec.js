const { test, expect } = require('@playwright/test');
const { TestHelpers } = require('./helpers/test-helpers');

test.describe('AI Company Playbook - Comprehensive Integration Tests', () => {
  
  test('should complete full user journey - browse all sections', async ({ page }) => {
    const errors = TestHelpers.setupConsoleErrorTracking(page);
    
    // Start journey
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
    
    // Navigate through main sections
    const sections = [
      { name: 'Playbook', url: '/playbook' },
      { name: 'Prompts', url: '/prompts' },
      { name: 'Requirements', url: '/requirements' },
      { name: 'Strategy', url: '/strategy' },
      { name: 'Courses', url: '/courses' }
    ];
    
    for (const section of sections) {
      console.log(`Navigating to ${section.name}...`);
      
      await page.goto(section.url);
      await page.waitForLoadState('networkidle');
      
      // Verify page loaded correctly
      expect(page.url()).toContain(section.url);
      
      // Check for main content
      const mainContent = page.locator('main, .main-content, h1, h2').first();
      await expect(mainContent).toBeVisible();
      
      // Take performance measurement
      const performance = await TestHelpers.getPagePerformance(page);
      console.log(`${section.name} load time: ${performance.loadComplete}ms`);
      expect(performance.loadComplete).toBeLessThan(10000);
    }
    
    // Check for any console errors during navigation
    if (errors.length > 0) {
      console.warn('Console errors detected:', errors);
    }
  });

  test('should handle all interactive elements end-to-end', async ({ page }) => {
    await page.goto('/');
    
    // Test main navigation
    const navLinks = page.locator('a[href^="/"]');
    const linkCount = Math.min(await navLinks.count(), 5);
    
    for (let i = 0; i < linkCount; i++) {
      const link = navLinks.nth(i);
      const href = await link.getAttribute('href');
      const linkText = await link.textContent();
      
      console.log(`Testing navigation: ${linkText} -> ${href}`);
      
      await link.click();
      await page.waitForLoadState('networkidle');
      
      // Verify navigation worked
      expect(page.url()).toContain(href);
      
      // Check for interactive elements on this page
      const buttons = page.locator('button, .btn, [role="button"]');
      const buttonCount = await buttons.count();
      
      if (buttonCount > 0) {
        console.log(`Found ${buttonCount} buttons on ${linkText} page`);
        
        // Test first button if available
        const firstButton = buttons.first();
        if (await firstButton.isVisible() && await firstButton.isEnabled()) {
          await firstButton.click();
          await page.waitForTimeout(1000);
        }
      }
      
      // Go back to home for next iteration
      await page.goto('/');
    }
  });

  test('should validate complete page accessibility', async ({ page }) => {
    const pagesToTest = ['/', '/playbook', '/prompts', '/strategy'];
    
    for (const pageUrl of pagesToTest) {
      console.log(`Testing accessibility for: ${pageUrl}`);
      
      await page.goto(pageUrl);
      await page.waitForLoadState('networkidle');
      
      // Check heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
      
      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        const src = await img.getAttribute('src');
        
        if (alt === null || alt === '') {
          console.warn(`Image without alt text: ${src}`);
        }
      }
      
      // Test keyboard navigation
      const focusableElements = page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
      const focusableCount = await focusableElements.count();
      
      if (focusableCount > 0) {
        await focusableElements.first().focus();
        
        // Tab through first few elements
        for (let i = 0; i < Math.min(focusableCount, 5); i++) {
          await page.keyboard.press('Tab');
          await page.waitForTimeout(100);
        }
      }
    }
  });

  test('should perform complete responsive design validation', async ({ page }) => {
    const testPages = ['/', '/playbook', '/prompts'];
    
    for (const pageUrl of testPages) {
      console.log(`Testing responsive design for: ${pageUrl}`);
      
      await page.goto(pageUrl);
      
      const responsiveResults = await TestHelpers.testResponsiveDesign(page);
      
      // Verify main content is visible at all breakpoints
      Object.entries(responsiveResults).forEach(([breakpointName, result]) => {
        console.log(`${pageUrl} at ${breakpointName}: ${result.mainContentVisible ? 'PASS' : 'FAIL'}`);
        expect(result.mainContentVisible).toBe(true);
      });
    }
  });

  test('should validate complete site performance', async ({ page }) => {
    const pagesToTest = ['/', '/playbook', '/prompts', '/strategy', '/courses'];
    const performanceResults = {};
    
    for (const pageUrl of pagesToTest) {
      console.log(`Testing performance for: ${pageUrl}`);
      
      const startTime = Date.now();
      await page.goto(pageUrl);
      await page.waitForLoadState('networkidle');
      const endTime = Date.now();
      
      const loadTime = endTime - startTime;
      const performance = await TestHelpers.getPagePerformance(page);
      
      performanceResults[pageUrl] = {
        totalLoadTime: loadTime,
        ...performance
      };
      
      // Performance assertions
      expect(loadTime).toBeLessThan(10000); // Less than 10 seconds
      expect(performance.timeToFirstByte).toBeLessThan(3000); // Less than 3 seconds TTFB
    }
    
    console.log('Performance Summary:', performanceResults);
  });

  test('should check for broken links across all pages', async ({ page }) => {
    const pagesToTest = ['/', '/playbook', '/prompts'];
    const allBrokenLinks = [];
    
    for (const pageUrl of pagesToTest) {
      console.log(`Checking links on: ${pageUrl}`);
      
      await page.goto(pageUrl);
      await page.waitForLoadState('networkidle');
      
      const brokenLinks = await TestHelpers.checkBrokenLinks(page);
      
      if (brokenLinks.length > 0) {
        console.warn(`Broken links found on ${pageUrl}:`, brokenLinks);
        allBrokenLinks.push(...brokenLinks);
      }
    }
    
    // Report all broken links
    if (allBrokenLinks.length > 0) {
      console.error('Total broken links found:', allBrokenLinks.length);
    }
  });

  test('should complete end-to-end mobile user journey', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Mobile user journey
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
    
    // Test mobile navigation (hamburger menu if exists)
    const mobileMenu = page.locator('.mobile-menu, .hamburger, .nav-toggle, [aria-label*="menu"]');
    
    if (await mobileMenu.count() > 0) {
      await mobileMenu.first().click();
      await page.waitForTimeout(500);
      
      // Check if menu opened
      const menuItems = page.locator('.mobile-menu-items, .nav-menu, .menu-items');
      if (await menuItems.count() > 0) {
        await expect(menuItems.first()).toBeVisible();
      }
    }
    
    // Test scrolling behavior on mobile
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2);
    });
    await page.waitForTimeout(500);
    
    // Test touch interactions if supported
    const interactiveElements = page.locator('button, a, .clickable');
    const elementCount = Math.min(await interactiveElements.count(), 3);
    
    for (let i = 0; i < elementCount; i++) {
      const element = interactiveElements.nth(i);
      
      if (await element.isVisible()) {
        // Simulate touch tap
        await element.tap();
        await page.waitForTimeout(500);
      }
    }
  });

  test('should validate complete form functionality (if forms exist)', async ({ page }) => {
    const pagesToTest = ['/', '/playbook', '/prompts', '/strategy'];
    
    for (const pageUrl of pagesToTest) {
      await page.goto(pageUrl);
      await page.waitForLoadState('networkidle');
      
      const forms = page.locator('form');
      const formCount = await forms.count();
      
      if (formCount > 0) {
        console.log(`Testing ${formCount} form(s) on ${pageUrl}`);
        
        for (let i = 0; i < formCount; i++) {
          const form = forms.nth(i);
          
          // Test form validation
          await TestHelpers.testFormValidation(form);
          
          // Test form submission (with valid data)
          const textInputs = form.locator('input[type="text"], input[type="email"], textarea');
          const textInputCount = await textInputs.count();
          
          for (let j = 0; j < textInputCount; j++) {
            const input = textInputs.nth(j);
            const inputType = await input.getAttribute('type');
            
            if (inputType === 'email') {
              await input.fill('<EMAIL>');
            } else {
              await input.fill('Test input value');
            }
          }
          
          // Find submit button
          const submitBtn = form.locator('button[type="submit"], input[type="submit"]');
          
          if (await submitBtn.count() > 0) {
            console.log('Testing form submission...');
            await submitBtn.click();
            await page.waitForLoadState('networkidle');
            
            // Check for success/error messages
            const messages = page.locator('.success, .error, .message, .alert');
            if (await messages.count() > 0) {
              const messageText = await messages.first().textContent();
              console.log(`Form submission result: ${messageText}`);
            }
          }
        }
      }
    }
  });
});
