const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Example Custom Tests', () => {
  
  test('example: how to test specific button functionality', async ({ page }) => {
    await page.goto('/');
    
    // Example: Test a specific button by its text
    const specificButton = page.locator('button:has-text("Learn More")');
    
    if (await specificButton.count() > 0) {
      await specificButton.click();
      // Add your specific assertions here
      await page.waitForLoadState('networkidle');
    }
  });

  test('example: how to test form submission', async ({ page }) => {
    await page.goto('/prompts'); // Example page with forms
    
    // Look for search or input forms
    const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
    
    if (await searchInput.count() > 0) {
      await searchInput.fill('artificial intelligence');
      await searchInput.press('Enter');
      
      await page.waitForLoadState('networkidle');
      
      // Verify search results or page change
      expect(page.url()).toMatch(/search|query|filter/);
    }
  });

  test('example: how to test specific page content', async ({ page }) => {
    await page.goto('/strategy');
    
    // Test for specific content that should be on the strategy page
    await expect(page.locator('h1, h2, h3')).toContainText(/strategy|planning|goal/i);
    
    // Test for specific sections or features
    const strategySections = page.locator('.strategy-section, .planning-item, .strategic-goal');
    
    if (await strategySections.count() > 0) {
      await expect(strategySections.first()).toBeVisible();
    }
  });

  test('example: how to test analytics dashboard', async ({ page }) => {
    await page.goto('/');
    
    // Test analytics cards/metrics that were visible in the website
    const analyticsNumbers = page.locator('text=/\\d+/'); // Matches any numbers
    
    if (await analyticsNumbers.count() > 0) {
      // Verify numbers are displayed
      await expect(analyticsNumbers.first()).toBeVisible();
      
      // Check if metrics make sense (e.g., should be positive numbers)
      const firstNumber = await analyticsNumbers.first().textContent();
      const number = parseInt(firstNumber.replace(/[^\d]/g, ''));
      expect(number).toBeGreaterThanOrEqual(0);
    }
  });

  test('example: how to test LinkedIn Posts section', async ({ page }) => {
    await page.goto('/linkedin-posts');
    
    // Test for LinkedIn-specific content
    await expect(page.locator('h1, h2')).toContainText(/linkedin/i);
    
    // Look for post-related elements
    const postElements = page.locator('.post, .linkedin-post, .social-post');
    const linkElements = page.locator('a[href*="linkedin.com"]');
    
    if (await postElements.count() > 0) {
      await expect(postElements.first()).toBeVisible();
    }
    
    if (await linkElements.count() > 0) {
      const href = await linkElements.first().getAttribute('href');
      expect(href).toContain('linkedin.com');
    }
  });

  test('example: how to test courses section', async ({ page }) => {
    await page.goto('/courses');
    
    // Test for course-related content
    await expect(page.locator('h1, h2')).toContainText(/course|learning|education/i);
    
    // Look for course listings or educational content
    const courseElements = page.locator('.course, .learning-module, .education-item');
    
    if (await courseElements.count() > 0) {
      console.log(`Found ${await courseElements.count()} course elements`);
      await expect(courseElements.first()).toBeVisible();
    }
  });

  test('example: how to test industry domains section', async ({ page }) => {
    await page.goto('/industry-domains');
    
    // Test for industry-specific content
    await expect(page.locator('h1, h2')).toContainText(/industry|domain|sector/i);
    
    // Look for domain/industry listings
    const domainElements = page.locator('.domain, .industry, .sector');
    
    if (await domainElements.count() > 0) {
      console.log(`Found ${await domainElements.count()} domain elements`);
      await expect(domainElements.first()).toBeVisible();
    }
  });

  test('example: how to test application library', async ({ page }) => {
    await page.goto('/app-info');
    
    // Test for application-related content
    await expect(page.locator('h1, h2')).toContainText(/app|application|software/i);
    
    // Look for application listings
    const appElements = page.locator('.app, .application, .software');
    
    if (await appElements.count() > 0) {
      console.log(`Found ${await appElements.count()} application elements`);
      await expect(appElements.first()).toBeVisible();
    }
  });

  test('example: how to test tech checklist', async ({ page }) => {
    await page.goto('/tech-checklist');
    
    // Test for checklist functionality
    await expect(page.locator('h1, h2')).toContainText(/tech|checklist|technology/i);
    
    // Look for checklist items or checkboxes
    const checklistItems = page.locator('.checklist-item, .checkbox, input[type="checkbox"]');
    
    if (await checklistItems.count() > 0) {
      console.log(`Found ${await checklistItems.count()} checklist items`);
      
      // Test checkbox functionality
      const firstCheckbox = checklistItems.first();
      if (await firstCheckbox.getAttribute('type') === 'checkbox') {
        const isChecked = await firstCheckbox.isChecked();
        await firstCheckbox.click();
        
        // Verify state changed
        const newState = await firstCheckbox.isChecked();
        expect(newState).toBe(!isChecked);
      }
    }
  });
});

/**
 * This file demonstrates how to create custom tests for specific features
 * of the AI Company Playbook website. You can use these patterns to:
 * 
 * 1. Test specific buttons or interactive elements
 * 2. Validate form submissions and search functionality
 * 3. Check for specific content on different pages
 * 4. Test analytics and dashboard features
 * 5. Validate section-specific functionality
 * 
 * To add your own tests:
 * 1. Copy one of the example patterns above
 * 2. Modify the selectors to match your specific elements
 * 3. Add appropriate assertions for your use case
 * 4. Run the test to verify it works as expected
 */
