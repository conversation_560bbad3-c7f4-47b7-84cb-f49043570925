const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Security Tests', () => {
  
  test('should have HTTPS enabled', async ({ page }) => {
    await page.goto('/');
    const url = page.url();
    expect(url).toMatch(/^https:/);
  });

  test('should have proper security headers', async ({ page }) => {
    const response = await page.goto('/');
    const headers = response.headers();
    
    // Check for security headers
    const securityHeaders = [
      'x-frame-options',
      'x-content-type-options',
      'x-xss-protection',
      'content-security-policy',
      'strict-transport-security'
    ];
    
    securityHeaders.forEach(header => {
      if (headers[header]) {
        console.log(`✓ ${header}: ${headers[header]}`);
      } else {
        console.warn(`✗ Missing security header: ${header}`);
      }
    });
  });

  test('should not expose sensitive information in source code', async ({ page }) => {
    await page.goto('/');
    
    const content = await page.content();
    
    // Check for potential sensitive data patterns
    const sensitivePatterns = [
      /password\s*[:=]\s*['"]\w+['"]/i,
      /api[_-]?key\s*[:=]\s*['"]\w+['"]/i,
      /secret\s*[:=]\s*['"]\w+['"]/i,
      /token\s*[:=]\s*['"]\w+['"]/i,
      /private[_-]?key/i,
      /connection[_-]?string/i
    ];
    
    sensitivePatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        console.warn(`Potential sensitive data found: ${matches[0]}`);
      }
    });
  });

  test('should handle SQL injection attempts', async ({ page }) => {
    await page.goto('/');
    
    // Find search inputs or form fields
    const inputs = page.locator('input[type="text"], input[type="search"], textarea');
    const inputCount = await inputs.count();
    
    const sqlInjectionPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --"
    ];
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      
      if (await input.isVisible() && await input.isEnabled()) {
        for (const payload of sqlInjectionPayloads) {
          await input.fill(payload);
          
          // Try submitting if there's a form
          const form = input.locator('xpath=ancestor::form');
          if (await form.count() > 0) {
            const submitBtn = form.locator('button[type="submit"], input[type="submit"]');
            
            if (await submitBtn.count() > 0) {
              await submitBtn.click();
              await page.waitForLoadState('networkidle');
              
              // Check that no database errors are exposed
              const content = await page.content();
              const errorPatterns = [
                /sql.*error/i,
                /mysql.*error/i,
                /oracle.*error/i,
                /postgresql.*error/i,
                /syntax.*error.*near/i
              ];
              
              errorPatterns.forEach(pattern => {
                if (pattern.test(content)) {
                  console.warn(`SQL error potentially exposed with payload: ${payload}`);
                }
              });
              
              // Navigate back
              await page.goto('/');
            }
          }
          
          await input.clear();
        }
      }
    }
  });

  test('should handle XSS attempts', async ({ page }) => {
    await page.goto('/');
    
    // Find inputs that might reflect user input
    const inputs = page.locator('input[type="text"], input[type="search"], textarea');
    const inputCount = await inputs.count();
    
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(\'XSS\')">',
      'javascript:alert("XSS")',
      '<svg onload="alert(\'XSS\')">'
    ];
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      
      if (await input.isVisible() && await input.isEnabled()) {
        for (const payload of xssPayloads) {
          await input.fill(payload);
          
          // Submit form if available
          const form = input.locator('xpath=ancestor::form');
          if (await form.count() > 0) {
            const submitBtn = form.locator('button[type="submit"], input[type="submit"]');
            
            if (await submitBtn.count() > 0) {
              await submitBtn.click();
              await page.waitForLoadState('networkidle');
              
              // Check if payload was reflected without encoding
              const content = await page.content();
              if (content.includes(payload)) {
                console.warn(`Potential XSS vulnerability with payload: ${payload}`);
              }
              
              // Navigate back
              await page.goto('/');
            }
          }
          
          await input.clear();
        }
      }
    }
  });

  test('should not allow clickjacking', async ({ page }) => {
    // Test if page can be embedded in iframe
    const response = await page.goto('/');
    const headers = response.headers();
    
    const xFrameOptions = headers['x-frame-options'];
    const csp = headers['content-security-policy'];
    
    let protected = false;
    
    if (xFrameOptions && (xFrameOptions.includes('DENY') || xFrameOptions.includes('SAMEORIGIN'))) {
      protected = true;
      console.log('✓ Protected by X-Frame-Options');
    }
    
    if (csp && csp.includes('frame-ancestors')) {
      protected = true;
      console.log('✓ Protected by CSP frame-ancestors');
    }
    
    if (!protected) {
      console.warn('✗ Page may be vulnerable to clickjacking');
    }
  });

  test('should validate file upload security (if applicable)', async ({ page }) => {
    await page.goto('/');
    
    // Look for file upload inputs
    const fileInputs = page.locator('input[type="file"]');
    const fileInputCount = await fileInputs.count();
    
    if (fileInputCount > 0) {
      console.log(`Found ${fileInputCount} file upload fields`);
      
      for (let i = 0; i < fileInputCount; i++) {
        const fileInput = fileInputs.nth(i);
        
        // Check if accept attribute is set
        const accept = await fileInput.getAttribute('accept');
        if (!accept) {
          console.warn('File input without accept attribute - may allow any file type');
        } else {
          console.log(`File input accepts: ${accept}`);
        }
      }
    }
  });

  test('should check for information disclosure', async ({ page }) => {
    // Check common paths that might expose information
    const testPaths = [
      '/robots.txt',
      '/.git',
      '/.env',
      '/config',
      '/admin',
      '/debug',
      '/test',
      '/backup'
    ];
    
    for (const path of testPaths) {
      const response = await page.goto(path, { waitUntil: 'networkidle' });
      
      if (response.status() === 200) {
        const content = await page.content();
        
        if (path === '/robots.txt') {
          console.log('robots.txt found (this is normal)');
        } else if (content.length > 100) { // Substantial content
          console.warn(`Potentially sensitive path accessible: ${path}`);
        }
      }
    }
    
    // Go back to homepage
    await page.goto('/');
  });

  test('should check for secure cookies (if any)', async ({ page, context }) => {
    // Set up to capture cookies
    await page.goto('/');
    
    // Trigger any authentication or cookie-setting actions
    // (This would need to be customized based on the actual site functionality)
    
    const cookies = await context.cookies();
    
    cookies.forEach(cookie => {
      console.log(`Cookie: ${cookie.name}`);
      
      if (!cookie.secure && page.url().startsWith('https:')) {
        console.warn(`Cookie ${cookie.name} not marked as secure`);
      }
      
      if (!cookie.httpOnly && cookie.name.toLowerCase().includes('session')) {
        console.warn(`Session cookie ${cookie.name} not marked as httpOnly`);
      }
      
      if (cookie.sameSite === 'None' && !cookie.secure) {
        console.warn(`Cookie ${cookie.name} has SameSite=None but not Secure`);
      }
    });
  });

  test('should validate CSRF protection (if applicable)', async ({ page }) => {
    await page.goto('/');
    
    // Look for forms
    const forms = page.locator('form');
    const formCount = await forms.count();
    
    for (let i = 0; i < formCount; i++) {
      const form = forms.nth(i);
      const method = await form.getAttribute('method');
      
      if (method && method.toLowerCase() === 'post') {
        // Check for CSRF token
        const csrfToken = form.locator('input[name*="csrf"], input[name*="token"], input[name="_token"]');
        
        if (await csrfToken.count() === 0) {
          console.warn('POST form without apparent CSRF protection');
        } else {
          console.log('CSRF token found in form');
        }
      }
    }
  });
});
