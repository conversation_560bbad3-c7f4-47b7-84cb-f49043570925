# AI Company Playbook - Playwright Test Suite

A comprehensive test automation suite built with <PERSON><PERSON> to test all buttons, features, and functionality of the AI Company Playbook website at https://ai-company-playbook.azurewebsites.net/.

## Overview

This project provides thorough automated testing coverage including:

- ✅ **Homepage functionality** - Layout, navigation, and core features
- ✅ **Navigation testing** - All internal and external links
- ✅ **Interactive elements** - Buttons, forms, modals, and user interactions
- ✅ **Accessibility compliance** - WCAG guidelines and keyboard navigation
- ✅ **Performance monitoring** - Load times, Core Web Vitals, and resource optimization
- ✅ **Security testing** - Basic vulnerability checks and security headers
- ✅ **Cross-browser compatibility** - Chrome, Firefox, Safari, and mobile devices
- ✅ **Integration testing** - End-to-end user journeys

## Quick Start

### Prerequisites

- Node.js (version 16 or higher)
- npm

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Install Playwright browsers:
   ```bash
   npm run install-browsers
   ```

### Running Tests

**Run all tests:**
```bash
npm test
```

**Run tests with browser UI (headed mode):**
```bash
npm run test:headed
```

**Run tests in debug mode:**
```bash
npm run test:debug
```

**Run tests with Playwright UI:**
```bash
npm run test:ui
```

**Run specific test categories:**
```bash
npm run test:homepage      # Homepage tests only
npm run test:navigation    # Navigation tests only
npm run test:interactive   # Interactive element tests
npm run test:accessibility # Accessibility tests
npm run test:performance   # Performance tests
npm run test:security      # Security tests
npm run test:integration   # Integration tests
```

**Run tests on specific browsers:**
```bash
npm run test:chromium      # Chrome/Chromium only
npm run test:firefox       # Firefox only
npm run test:webkit        # Safari/WebKit only
npm run test:mobile        # Mobile devices only
```

**View test results:**
```bash
npm run report
```

## Test Structure

```
tests/
├── homepage.spec.js           # Homepage functionality tests
├── navigation.spec.js         # Navigation and routing tests
├── interactive-elements.spec.js # Button and form interaction tests
├── accessibility.spec.js     # Accessibility compliance tests
├── performance.spec.js       # Performance and load time tests
├── security.spec.js          # Security vulnerability tests
├── cross-browser.spec.js     # Cross-browser compatibility tests
├── integration.spec.js       # End-to-end integration tests
└── helpers/
    └── test-helpers.js       # Reusable test utilities
```

## Test Categories Explained

### 🏠 Homepage Tests (`homepage.spec.js`)
- Page load verification
- Title and meta description checks
- Analytics overview display
- Navigation link validation
- Responsive design on mobile
- Image loading verification
- Heading structure validation

### 🧭 Navigation Tests (`navigation.spec.js`)
- All internal page navigation
- External link handling
- Back button functionality
- Breadcrumb navigation (if available)
- Navigation consistency across pages

### 🖱️ Interactive Elements (`interactive-elements.spec.js`)
- All button click functionality
- Form input testing
- Dropdown/select element testing
- Modal dialog interactions
- Accordion/collapsible elements
- Tab functionality
- Search functionality
- Image interactions

### ♿ Accessibility Tests (`accessibility.spec.js`)
- Heading hierarchy validation
- Image alt text verification
- Form label associations
- Keyboard navigation testing
- Focus management
- ARIA attributes validation
- Color contrast checks
- Skip link functionality

### ⚡ Performance Tests (`performance.spec.js`)
- Page load time measurement
- Resource size analysis
- Concurrent user simulation
- Core Web Vitals measurement
- Memory leak detection
- Network condition testing
- Resource optimization checks
- Caching header validation

### 🔒 Security Tests (`security.spec.js`)
- HTTPS enforcement
- Security header validation
- Sensitive information exposure checks
- SQL injection attempt testing
- XSS vulnerability testing
- Clickjacking protection
- File upload security (if applicable)
- Information disclosure checks
- Cookie security validation
- CSRF protection verification

### 🌐 Cross-Browser Tests (`cross-browser.spec.js`)
- Consistent functionality across browsers
- Browser-specific feature testing
- JavaScript compatibility validation

### 🔄 Integration Tests (`integration.spec.js`)
- Complete user journey testing
- End-to-end workflow validation
- Multi-page interaction testing
- Form submission workflows
- Mobile user experience testing

## Configuration

The project uses `playwright.config.js` for configuration:

- **Base URL**: https://ai-company-playbook.azurewebsites.net
- **Browsers**: Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari, Edge
- **Reporters**: HTML, JSON, List
- **Screenshots**: On failure
- **Videos**: On failure
- **Traces**: On retry

## Test Utilities

The `test-helpers.js` file provides common utilities:

- Image loading verification
- Viewport testing
- Element scrolling
- Screenshot capture
- Console error tracking
- Performance measurement
- Broken link detection
- Network simulation
- Form validation testing
- Responsive design testing

## Continuous Integration

This test suite is designed to work with CI/CD pipelines. Set the `CI` environment variable to enable:
- Retry on failure (2 retries)
- Single worker process
- Headless mode

## Troubleshooting

**Tests failing with timeout errors:**
- Increase timeout in `playwright.config.js`
- Check network connectivity
- Verify the website is accessible

**Browser installation issues:**
- Run `npm run install-browsers`
- Check system requirements for Playwright

**Permission errors:**
- Ensure proper file permissions
- Run with appropriate user privileges

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use descriptive test names
3. Add proper assertions
4. Include error handling
5. Update documentation

## Reports

Test results are available in multiple formats:
- **HTML Report**: Visual test results with screenshots and videos
- **JSON Report**: Machine-readable test results
- **Console Output**: Real-time test execution feedback

Access the HTML report after test execution:
```bash
npm run report
```

## Website Features Tested

Based on the AI Company Playbook website structure:

✅ **Main Navigation**
- Home page
- Playbook section
- Prompts library
- Requirements tracking
- Strategy documentation
- LinkedIn Posts management
- Tech checklist
- Courses catalog
- Industry domains
- Applications library

✅ **Analytics Dashboard**
- Total items count
- Active modules display
- Learning hours tracking
- Domain statistics

✅ **Additional Features**
- DevOps documentation
- AI Tools section
- Tech Websites collection
- AI Videos library
- Digital Marketing resources
- Sales techniques
- All interactive elements and buttons

This comprehensive test suite ensures the AI Company Playbook website functions correctly across all browsers and devices, providing confidence in the user experience and site reliability.
