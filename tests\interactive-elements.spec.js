const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Interactive Elements Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should test all clickable buttons', async ({ page }) => {
    // Find all button elements
    const buttons = page.locator('button, input[type="button"], input[type="submit"], .btn, [role="button"]');
    const buttonCount = await buttons.count();
    
    console.log(`Found ${buttonCount} buttons`);
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      
      // Check if button is visible and enabled
      if (await button.isVisible() && await button.isEnabled()) {
        const buttonText = await button.textContent();
        console.log(`Testing button: ${buttonText || `Button ${i + 1}`}`);
        
        // Test button click
        await button.click();
        
        // Wait for any potential navigation or modal
        await page.waitForTimeout(1000);
        
        // Check if click resulted in navigation
        const currentUrl = page.url();
        console.log(`After click, URL: ${currentUrl}`);
        
        // If navigated away, go back to test page
        if (!currentUrl.includes('ai-company-playbook.azurewebsites.net') || 
            (!currentUrl.endsWith('/') && !currentUrl.includes('index'))) {
          await page.goto('/');
          await page.waitForLoadState('networkidle');
        }
      }
    }
  });

  test('should test all form inputs', async ({ page }) => {
    // Find all input elements
    const inputs = page.locator('input[type="text"], input[type="email"], input[type="password"], input[type="search"], textarea');
    const inputCount = await inputs.count();
    
    console.log(`Found ${inputCount} input fields`);
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      
      if (await input.isVisible() && await input.isEnabled()) {
        const inputType = await input.getAttribute('type') || 'text';
        const placeholder = await input.getAttribute('placeholder') || '';
        
        console.log(`Testing input ${i + 1}: type=${inputType}, placeholder=${placeholder}`);
        
        // Test input functionality
        await input.clear();
        await input.fill('Test input value');
        
        const value = await input.inputValue();
        expect(value).toBe('Test input value');
        
        // Clear for next test
        await input.clear();
      }
    }
  });

  test('should test dropdown/select elements', async ({ page }) => {
    const selects = page.locator('select');
    const selectCount = await selects.count();
    
    console.log(`Found ${selectCount} select elements`);
    
    for (let i = 0; i < selectCount; i++) {
      const select = selects.nth(i);
      
      if (await select.isVisible()) {
        const options = select.locator('option');
        const optionCount = await options.count();
        
        if (optionCount > 1) {
          // Test selecting different options
          await select.selectOption({ index: 1 });
          
          const selectedValue = await select.inputValue();
          console.log(`Selected value: ${selectedValue}`);
        }
      }
    }
  });

  test('should test modal dialogs (if any)', async ({ page }) => {
    // Look for modal triggers
    const modalTriggers = page.locator('[data-toggle="modal"], [data-target*="modal"], .modal-trigger');
    const triggerCount = await modalTriggers.count();
    
    for (let i = 0; i < triggerCount; i++) {
      const trigger = modalTriggers.nth(i);
      
      if (await trigger.isVisible()) {
        await trigger.click();
        
        // Wait for modal to appear
        const modal = page.locator('.modal, .dialog, [role="dialog"]');
        await expect(modal.first()).toBeVisible({ timeout: 5000 });
        
        // Look for close button
        const closeButton = page.locator('.modal .close, .modal .btn-close, .dialog .close, [aria-label="Close"]');
        if (await closeButton.count() > 0) {
          await closeButton.first().click();
          await expect(modal.first()).not.toBeVisible();
        } else {
          // Try pressing Escape
          await page.keyboard.press('Escape');
        }
      }
    }
  });

  test('should test accordion/collapsible elements', async ({ page }) => {
    // Look for accordion elements
    const accordions = page.locator('.accordion, .collapse, [data-toggle="collapse"], .collapsible');
    const accordionCount = await accordions.count();
    
    for (let i = 0; i < accordionCount; i++) {
      const accordion = accordions.nth(i);
      
      if (await accordion.isVisible()) {
        // Find accordion trigger
        const trigger = accordion.locator('.accordion-header, .accordion-toggle, [data-toggle="collapse"]');
        
        if (await trigger.count() > 0) {
          await trigger.first().click();
          await page.waitForTimeout(500);
          
          // Check if content is visible
          const content = accordion.locator('.accordion-body, .accordion-content, .collapse-content');
          if (await content.count() > 0) {
            // Toggle again to close
            await trigger.first().click();
            await page.waitForTimeout(500);
          }
        }
      }
    }
  });

  test('should test tabs (if any)', async ({ page }) => {
    // Look for tab elements
    const tabs = page.locator('.nav-tabs, .tabs, [role="tablist"]');
    
    if (await tabs.count() > 0) {
      const tabItems = tabs.first().locator('.nav-item, .tab, [role="tab"]');
      const tabCount = await tabItems.count();
      
      for (let i = 0; i < tabCount; i++) {
        const tab = tabItems.nth(i);
        
        if (await tab.isVisible()) {
          await tab.click();
          await page.waitForTimeout(500);
          
          // Check if corresponding tab panel is visible
          const tabPanel = page.locator('[role="tabpanel"]:visible, .tab-pane.active');
          if (await tabPanel.count() > 0) {
            await expect(tabPanel.first()).toBeVisible();
          }
        }
      }
    }
  });

  test('should test search functionality', async ({ page }) => {
    // Look for search inputs
    const searchInputs = page.locator('input[type="search"], input[placeholder*="search" i], .search-input');
    const searchCount = await searchInputs.count();
    
    for (let i = 0; i < searchCount; i++) {
      const searchInput = searchInputs.nth(i);
      
      if (await searchInput.isVisible()) {
        await searchInput.fill('test search');
        
        // Look for search button or form
        const searchButton = page.locator('button[type="submit"], .search-button, input[type="submit"]').first();
        
        if (await searchButton.count() > 0 && await searchButton.isVisible()) {
          await searchButton.click();
          await page.waitForLoadState('networkidle');
        } else {
          // Try pressing Enter
          await searchInput.press('Enter');
          await page.waitForLoadState('networkidle');
        }
        
        // Navigate back if search redirected
        if (!page.url().includes('ai-company-playbook.azurewebsites.net') || 
            page.url().includes('search')) {
          await page.goto('/');
          await page.waitForLoadState('networkidle');
        }
      }
    }
  });

  test('should test image interactions', async ({ page }) => {
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < Math.min(imageCount, 5); i++) { // Test first 5 images
      const image = images.nth(i);
      
      if (await image.isVisible()) {
        // Test image click (might open lightbox or navigate)
        const imageSrc = await image.getAttribute('src');
        console.log(`Testing image: ${imageSrc}`);
        
        await image.click();
        await page.waitForTimeout(1000);
        
        // Check if lightbox or modal opened
        const lightbox = page.locator('.lightbox, .modal, .image-modal, [role="dialog"]');
        if (await lightbox.count() > 0 && await lightbox.first().isVisible()) {
          // Close lightbox
          const closeBtn = page.locator('.close, .btn-close, [aria-label="Close"]');
          if (await closeBtn.count() > 0) {
            await closeBtn.first().click();
          } else {
            await page.keyboard.press('Escape');
          }
        }
      }
    }
  });
});
