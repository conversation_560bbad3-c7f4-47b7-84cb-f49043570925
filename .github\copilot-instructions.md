<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# AI Company Playbook Test Suite

This is a comprehensive Playwright test automation project for testing the AI Company Playbook website at https://ai-company-playbook.azurewebsites.net/.

## Project Guidelines

When working with this codebase:

1. **Test Structure**: Follow the existing test organization with separate spec files for different test categories
2. **Page Object Model**: Consider implementing page object patterns for complex page interactions
3. **Test Data**: Use meaningful test data and avoid hardcoded values where possible
4. **Error Handling**: Include proper error handling and meaningful assertions
5. **Documentation**: Add comments for complex test logic and edge cases

## Test Categories

- **Homepage Tests**: Basic functionality and layout validation
- **Navigation Tests**: Link functionality and routing validation
- **Interactive Elements**: Button clicks, forms, and user interactions
- **Accessibility**: WCAG compliance and keyboard navigation
- **Performance**: Load times, resource optimization, and Core Web Vitals
- **Security**: Basic security checks and vulnerability testing
- **Cross-Browser**: Compatibility testing across different browsers
- **Integration**: End-to-end user journeys and complete workflows

## Best Practices

- Use descriptive test names that explain what is being tested
- Group related tests using `test.describe()`
- Use `test.beforeEach()` for common setup
- Implement proper waiting strategies with `waitForLoadState()` and `waitForSelector()`
- Take screenshots on failures for debugging
- Use helper functions for common operations
- Keep tests independent and idempotent

## Configuration

The project uses Playwright configuration with:
- Multiple browser support (Chromium, Firefox, WebKit)
- Mobile device testing
- HTML, JSON, and list reporters
- Screenshot and video capture on failures
- Trace collection for debugging
