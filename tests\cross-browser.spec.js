const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Cross-Browser Compatibility', () => {
  
  test('should work consistently across browsers', async ({ page, browserName }) => {
    await page.goto('/');
    
    console.log(`Testing on: ${browserName}`);
    
    // Basic functionality should work on all browsers
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h2:has-text("Analytics Overview")')).toBeVisible();
    
    // Test navigation links
    const links = page.locator('a[href^="/"]');
    const linkCount = Math.min(await links.count(), 3);
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      const href = await link.getAttribute('href');
      
      await link.click();
      await page.waitForLoadState('networkidle');
      
      // Should navigate successfully
      expect(page.url()).toContain(href);
      
      // Go back
      await page.goBack();
      await page.waitForLoadState('networkidle');
    }
  });

  test('should handle browser-specific features', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Test features that might behave differently across browsers
    const images = page.locator('img');
    if (await images.count() > 0) {
      // Test image loading
      const firstImage = images.first();
      await expect(firstImage).toBeVisible();
      
      const naturalWidth = await firstImage.evaluate(img => img.naturalWidth);
      expect(naturalWidth).toBeGreaterThan(0);
    }
    
    // Test CSS features
    const computedStyles = await page.evaluate(() => {
      const element = document.querySelector('h1');
      if (element) {
        const styles = window.getComputedStyle(element);
        return {
          display: styles.display,
          fontSize: styles.fontSize,
          color: styles.color
        };
      }
      return null;
    });
    
    if (computedStyles) {
      expect(computedStyles.display).toBeTruthy();
      console.log(`${browserName} styles:`, computedStyles);
    }
  });

  test('should handle JavaScript features consistently', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Test basic JavaScript functionality
    const jsSupport = await page.evaluate(() => {
      return {
        localStorage: typeof localStorage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        fetch: typeof fetch !== 'undefined',
        promises: typeof Promise !== 'undefined',
        asyncAwait: (async () => true)() instanceof Promise
      };
    });
    
    console.log(`${browserName} JS support:`, jsSupport);
    
    // Modern browsers should support these features
    expect(jsSupport.promises).toBe(true);
    expect(jsSupport.fetch).toBe(true);
  });
});
