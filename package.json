{"name": "qa2", "version": "1.0.0", "description": "Comprehensive Playwright test suite for AI Company Playbook website", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:homepage": "playwright test tests/homepage.spec.js", "test:navigation": "playwright test tests/navigation.spec.js", "test:interactive": "playwright test tests/interactive-elements.spec.js", "test:accessibility": "playwright test tests/accessibility.spec.js", "test:performance": "playwright test tests/performance.spec.js", "test:security": "playwright test tests/security.spec.js", "test:integration": "playwright test tests/integration.spec.js", "test:cross-browser": "playwright test tests/cross-browser.spec.js", "report": "playwright show-report", "install-browsers": "playwright install"}, "keywords": ["playwright", "testing", "e2e", "automation", "qa"], "author": "Ensar Solutions", "license": "ISC", "type": "commonjs", "dependencies": {"@playwright/test": "^1.54.2"}}