const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Accessibility Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    const headingCount = await headings.count();
    
    expect(headingCount).toBeGreaterThan(0);
    
    // Check that there's only one h1
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBe(1);
    
    // Check heading text content
    for (let i = 0; i < headingCount; i++) {
      const heading = headings.nth(i);
      const text = await heading.textContent();
      expect(text.trim()).not.toBe('');
    }
  });

  test('should have alt text for images', async ({ page }) => {
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      const src = await img.getAttribute('src');
      
      // Alt text should exist and not be empty (or be decorative)
      if (alt === null) {
        console.warn(`Image missing alt text: ${src}`);
      } else if (alt.trim() === '') {
        // Empty alt is okay for decorative images
        console.log(`Decorative image (empty alt): ${src}`);
      }
    }
  });

  test('should have proper link text', async ({ page }) => {
    const links = page.locator('a');
    const linkCount = await links.count();
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      const text = await link.textContent();
      const href = await link.getAttribute('href');
      const ariaLabel = await link.getAttribute('aria-label');
      
      // Link should have meaningful text or aria-label
      if ((!text || text.trim() === '') && (!ariaLabel || ariaLabel.trim() === '')) {
        console.warn(`Link without accessible text: ${href}`);
      }
      
      // Avoid generic link text
      const genericTexts = ['click here', 'read more', 'more', 'link'];
      if (text && genericTexts.includes(text.toLowerCase().trim())) {
        console.warn(`Generic link text found: "${text}" for ${href}`);
      }
    }
  });

  test('should have form labels', async ({ page }) => {
    const inputs = page.locator('input:not([type="hidden"]), textarea, select');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      const placeholder = await input.getAttribute('placeholder');
      
      let hasLabel = false;
      
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        hasLabel = await label.count() > 0;
      }
      
      if (!hasLabel && !ariaLabel && !ariaLabelledBy) {
        const inputType = await input.getAttribute('type');
        console.warn(`Input without label: type=${inputType}, placeholder=${placeholder}`);
      }
    }
  });

  test('should have proper color contrast', async ({ page }) => {
    // Test color contrast by checking if text is readable
    const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span, div, a, button');
    const elementCount = Math.min(await textElements.count(), 20); // Test first 20 elements
    
    for (let i = 0; i < elementCount; i++) {
      const element = textElements.nth(i);
      
      if (await element.isVisible()) {
        const computedStyle = await element.evaluate(el => {
          const style = window.getComputedStyle(el);
          return {
            color: style.color,
            backgroundColor: style.backgroundColor,
            fontSize: style.fontSize
          };
        });
        
        // Log for manual review
        console.log(`Element ${i}: color=${computedStyle.color}, bg=${computedStyle.backgroundColor}`);
      }
    }
  });

  test('should be keyboard navigable', async ({ page }) => {
    // Test Tab navigation
    const focusableElements = page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const focusableCount = await focusableElements.count();
    
    if (focusableCount > 0) {
      // Start from the first focusable element
      await focusableElements.first().focus();
      
      // Tab through several elements
      for (let i = 0; i < Math.min(focusableCount, 10); i++) {
        await page.keyboard.press('Tab');
        await page.waitForTimeout(100);
        
        // Check if an element is focused
        const focusedElement = page.locator(':focus');
        const isVisible = await focusedElement.isVisible().catch(() => false);
        
        if (isVisible) {
          const tagName = await focusedElement.evaluate(el => el.tagName);
          console.log(`Focused element ${i + 1}: ${tagName}`);
        }
      }
    }
  });

  test('should have skip links (if available)', async ({ page }) => {
    // Look for skip navigation links
    const skipLinks = page.locator('a[href="#main"], a[href="#content"], .skip-link, .skip-nav');
    
    if (await skipLinks.count() > 0) {
      const skipLink = skipLinks.first();
      await expect(skipLink).toBeVisible();
      
      // Test skip link functionality
      await skipLink.click();
      
      // Check if focus moved to main content
      const mainContent = page.locator('#main, #content, main, [role="main"]');
      if (await mainContent.count() > 0) {
        await expect(mainContent.first()).toBeFocused();
      }
    }
  });

  test('should have proper ARIA attributes', async ({ page }) => {
    // Check for proper ARIA usage
    const elementsWithAria = page.locator('[aria-label], [aria-labelledby], [aria-describedby], [role]');
    const ariaCount = await elementsWithAria.count();
    
    for (let i = 0; i < ariaCount; i++) {
      const element = elementsWithAria.nth(i);
      const role = await element.getAttribute('role');
      const ariaLabel = await element.getAttribute('aria-label');
      const ariaLabelledBy = await element.getAttribute('aria-labelledby');
      
      console.log(`Element with ARIA: role=${role}, aria-label=${ariaLabel}, aria-labelledby=${ariaLabelledBy}`);
      
      // Validate ARIA labelledby references
      if (ariaLabelledBy) {
        const referencedElement = page.locator(`#${ariaLabelledBy}`);
        const exists = await referencedElement.count() > 0;
        expect(exists).toBe(true);
      }
    }
  });

  test('should handle focus management', async ({ page }) => {
    // Test that focus is visible and properly managed
    const interactiveElements = page.locator('a, button, input, select, textarea');
    const elementCount = Math.min(await interactiveElements.count(), 5);
    
    for (let i = 0; i < elementCount; i++) {
      const element = interactiveElements.nth(i);
      
      if (await element.isVisible()) {
        await element.focus();
        
        // Check if element has focus
        await expect(element).toBeFocused();
        
        // Check if focus is visible (outline or similar)
        const hasOutline = await element.evaluate(el => {
          const style = window.getComputedStyle(el);
          return style.outline !== 'none' || style.boxShadow !== 'none';
        });
        
        console.log(`Element ${i} focus visible: ${hasOutline}`);
      }
    }
  });

  test('should not have accessibility violations using basic checks', async ({ page }) => {
    // Basic accessibility checks
    
    // Check for missing page title
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.trim()).not.toBe('');
    
    // Check for missing lang attribute
    const lang = await page.locator('html').getAttribute('lang');
    if (!lang) {
      console.warn('Missing lang attribute on html element');
    }
    
    // Check for proper document structure
    const main = page.locator('main, [role="main"]');
    const mainCount = await main.count();
    
    if (mainCount === 0) {
      console.warn('No main content area found');
    } else if (mainCount > 1) {
      console.warn('Multiple main content areas found');
    }
  });
});
