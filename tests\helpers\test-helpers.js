const { test, expect } = require('@playwright/test');

// Helper functions for common test utilities
class TestHelpers {
  /**
   * Wait for all images to load on the page
   */
  static async waitForImages(page) {
    await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return Promise.all(
        images.map(img => {
          if (img.complete) return Promise.resolve();
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        })
      );
    });
  }

  /**
   * Check if element is in viewport
   */
  static async isInViewport(element) {
    return await element.evaluate(el => {
      const rect = el.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    });
  }

  /**
   * Scroll element into view smoothly
   */
  static async scrollToElement(element) {
    await element.evaluate(el => {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
    await element.page().waitForTimeout(500);
  }

  /**
   * Take a screenshot of specific element
   */
  static async screenshotElement(element, filename) {
    await element.screenshot({ path: `screenshots/${filename}` });
  }

  /**
   * Get all console errors from the page
   */
  static setupConsoleErrorTracking(page) {
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    return errors;
  }

  /**
   * Check page load performance
   */
  static async getPagePerformance(page) {
    return await page.evaluate(() => {
      const timing = performance.timing;
      return {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        timeToFirstByte: timing.responseStart - timing.navigationStart,
        domInteractive: timing.domInteractive - timing.navigationStart
      };
    });
  }

  /**
   * Check for broken links on the page
   */
  static async checkBrokenLinks(page) {
    const links = await page.locator('a[href]').all();
    const brokenLinks = [];

    for (const link of links.slice(0, 10)) { // Check first 10 links to avoid timeout
      const href = await link.getAttribute('href');
      
      if (href && href.startsWith('http')) {
        try {
          const response = await page.request.head(href);
          if (response.status() >= 400) {
            brokenLinks.push({ href, status: response.status() });
          }
        } catch (error) {
          brokenLinks.push({ href, error: error.message });
        }
      }
    }

    return brokenLinks;
  }

  /**
   * Simulate slow network connection
   */
  static async simulateSlowNetwork(page) {
    await page.route('**/*', async route => {
      // Add random delay between 100-500ms
      await new Promise(resolve => setTimeout(resolve, Math.random() * 400 + 100));
      await route.continue();
    });
  }

  /**
   * Test form validation
   */
  static async testFormValidation(form) {
    const inputs = await form.locator('input[required], textarea[required], select[required]').all();
    
    for (const input of inputs) {
      const type = await input.getAttribute('type');
      const tagName = await input.evaluate(el => el.tagName.toLowerCase());
      
      // Try submitting empty required field
      await input.clear();
      
      if (tagName === 'select') {
        await input.selectOption('');
      }
      
      // Look for validation message
      const validationMessage = await input.evaluate(el => el.validationMessage);
      if (validationMessage) {
        console.log(`Validation message for ${type || tagName}: ${validationMessage}`);
      }
    }
  }

  /**
   * Check for responsive design breakpoints
   */
  static async testResponsiveDesign(page) {
    const breakpoints = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1920, height: 1080 }
    ];

    const results = {};

    for (const breakpoint of breakpoints) {
      await page.setViewportSize({ width: breakpoint.width, height: breakpoint.height });
      await page.waitForTimeout(500);

      // Check if main elements are visible
      const mainElement = page.locator('main, .main-content, h1').first();
      const isVisible = await mainElement.isVisible();
      
      results[breakpoint.name] = {
        ...breakpoint,
        mainContentVisible: isVisible
      };
    }

    return results;
  }
}

module.exports = { TestHelpers };
