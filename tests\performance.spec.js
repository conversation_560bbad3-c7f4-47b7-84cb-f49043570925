const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Performance Tests', () => {
  
  test('should load page within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`Page load time: ${loadTime}ms`);
    
    // Page should load within 10 seconds
    expect(loadTime).toBeLessThan(10000);
  });

  test('should have reasonable resource sizes', async ({ page }) => {
    const responses = [];
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        size: response.headers()['content-length'] || 0
      });
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for large resources
    const largeResources = responses.filter(r => parseInt(r.size) > 1000000); // > 1MB
    
    if (largeResources.length > 0) {
      console.warn('Large resources found:', largeResources);
    }
    
    // Check for failed requests
    const failedRequests = responses.filter(r => r.status >= 400);
    expect(failedRequests).toHaveLength(0);
  });

  test('should handle concurrent users simulation', async ({ browser }) => {
    const contexts = [];
    const pages = [];
    
    // Simulate 5 concurrent users
    for (let i = 0; i < 5; i++) {
      const context = await browser.newContext();
      const page = await context.newPage();
      contexts.push(context);
      pages.push(page);
    }
    
    // All users navigate to homepage simultaneously
    const startTime = Date.now();
    await Promise.all(pages.map(page => page.goto('/')));
    await Promise.all(pages.map(page => page.waitForLoadState('networkidle')));
    const endTime = Date.now();
    
    console.log(`Concurrent load time: ${endTime - startTime}ms`);
    
    // Verify all pages loaded successfully
    for (const page of pages) {
      await expect(page.locator('h1')).toBeVisible();
    }
    
    // Clean up
    for (const context of contexts) {
      await context.close();
    }
  });

  test('should measure Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Measure performance metrics
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const vitals = {};
          
          entries.forEach(entry => {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime;
            }
            if (entry.name === 'largest-contentful-paint') {
              vitals.lcp = entry.startTime;
            }
          });
          
          resolve(vitals);
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
        
        // Fallback timeout
        setTimeout(() => resolve({}), 5000);
      });
    });
    
    console.log('Performance metrics:', metrics);
    
    // Basic performance assertions
    if (metrics.fcp) {
      expect(metrics.fcp).toBeLessThan(3000); // FCP should be < 3s
    }
    if (metrics.lcp) {
      expect(metrics.lcp).toBeLessThan(4000); // LCP should be < 4s
    }
  });

  test('should check for memory leaks during navigation', async ({ page }) => {
    const initialMemory = await page.evaluate(() => {
      if (performance.memory) {
        return performance.memory.usedJSHeapSize;
      }
      return 0;
    });
    
    // Navigate through several pages
    const pages = ['/', '/playbook', '/prompts', '/strategy', '/courses'];
    
    for (const url of pages) {
      await page.goto(url);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
    }
    
    const finalMemory = await page.evaluate(() => {
      if (performance.memory) {
        return performance.memory.usedJSHeapSize;
      }
      return 0;
    });
    
    if (initialMemory > 0 && finalMemory > 0) {
      const memoryIncrease = finalMemory - initialMemory;
      console.log(`Memory usage change: ${memoryIncrease} bytes`);
      
      // Memory shouldn't increase dramatically (> 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    }
  });

  test('should handle slow network conditions', async ({ page, context }) => {
    // Simulate slow 3G network
    await context.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });
    
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`Load time with slow network: ${loadTime}ms`);
    
    // Page should still be functional
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should check resource optimization', async ({ page }) => {
    const resources = [];
    
    page.on('response', response => {
      const url = response.url();
      const contentType = response.headers()['content-type'] || '';
      const contentLength = response.headers()['content-length'];
      
      resources.push({
        url,
        contentType,
        size: contentLength ? parseInt(contentLength) : 0,
        status: response.status()
      });
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for uncompressed resources
    const textResources = resources.filter(r => 
      r.contentType.includes('text/') || 
      r.contentType.includes('application/javascript') ||
      r.contentType.includes('application/json')
    );
    
    for (const resource of textResources) {
      if (resource.size > 10000) { // > 10KB
        console.log(`Large uncompressed resource: ${resource.url} (${resource.size} bytes)`);
      }
    }
    
    // Check for unused CSS/JS (basic check)
    const cssResources = resources.filter(r => r.contentType.includes('text/css'));
    const jsResources = resources.filter(r => r.contentType.includes('javascript'));
    
    console.log(`Loaded ${cssResources.length} CSS files, ${jsResources.length} JS files`);
  });

  test('should verify caching headers', async ({ page }) => {
    const staticResources = [];
    
    page.on('response', response => {
      const url = response.url();
      const contentType = response.headers()['content-type'] || '';
      const cacheControl = response.headers()['cache-control'];
      const etag = response.headers()['etag'];
      const lastModified = response.headers()['last-modified'];
      
      // Check static resources
      if (contentType.includes('image/') || 
          contentType.includes('text/css') || 
          contentType.includes('javascript') ||
          url.includes('.js') || url.includes('.css') || url.includes('.png') || url.includes('.jpg')) {
        
        staticResources.push({
          url,
          cacheControl,
          etag,
          lastModified
        });
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check if static resources have proper caching headers
    for (const resource of staticResources) {
      if (!resource.cacheControl && !resource.etag && !resource.lastModified) {
        console.warn(`Resource without caching headers: ${resource.url}`);
      }
    }
  });
});
