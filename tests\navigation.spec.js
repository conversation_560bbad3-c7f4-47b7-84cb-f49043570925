const { test, expect } = require('@playwright/test');

test.describe('AI Company Playbook - Navigation Tests', () => {
  
  const navigationPages = [
    { name: 'Playbook', url: '/playbook' },
    { name: 'Prompts', url: '/prompts' },
    { name: 'Requirements', url: '/requirements' },
    { name: 'Strategy', url: '/strategy' },
    { name: 'LinkedIn Posts', url: '/linkedin-posts' },
    { name: 'Tech Checklist', url: '/tech-checklist' },
    { name: 'Courses', url: '/courses' },
    { name: 'Industry Domains', url: '/industry-domains' },
    { name: 'Applications', url: '/app-info' },
    { name: 'DevOps', url: '/devops' },
    { name: 'AI Tools', url: '/ai-tools' },
    { name: 'Tech Websites', url: '/tech-websites' },
    { name: 'AI Videos', url: '/youtube-ai-videos' },
    { name: 'Digital Marketing', url: '/digital-marketing' },
    { name: 'Sales', url: '/sales-techniques' }
  ];

  navigationPages.forEach(({ name, url }) => {
    test(`should navigate to ${name} page and load successfully`, async ({ page }) => {
      await page.goto(url);
      
      // Check that page loads without errors
      await expect(page).not.toHaveURL(/.*error.*/);
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Check for page title or heading
      const pageTitle = page.locator('h1, h2, title');
      await expect(pageTitle.first()).toBeVisible({ timeout: 10000 });
      
      // Check that we're on the correct page
      expect(page.url()).toContain(url);
    });
  });

  test('should navigate between pages using navigation links', async ({ page }) => {
    await page.goto('/');
    
    // Click on Playbook link
    await page.click('a[href*="/playbook"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/playbook');
    
    // Go back to home
    await page.goto('/');
    
    // Click on Prompts link
    await page.click('a[href*="/prompts"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/prompts');
  });

  test('should handle back button navigation', async ({ page }) => {
    await page.goto('/');
    await page.click('a[href*="/strategy"]');
    await page.waitForLoadState('networkidle');
    
    // Go back using browser back button
    await page.goBack();
    await page.waitForLoadState('networkidle');
    
    // Should be back on homepage
    expect(page.url()).toMatch(/\/$|\/index/);
  });

  test('should open external links in new tab', async ({ context, page }) => {
    await page.goto('/');
    
    // Look for external links (links that don't start with / or current domain)
    const externalLinks = page.locator('a[href^="http"]:not([href*="ai-company-playbook.azurewebsites.net"])');
    const linkCount = await externalLinks.count();
    
    if (linkCount > 0) {
      const [newPage] = await Promise.all([
        context.waitForEvent('page'),
        externalLinks.first().click()
      ]);
      
      await newPage.waitForLoadState('networkidle');
      expect(newPage.url()).toMatch(/^https?:\/\//);
      await newPage.close();
    }
  });

  test('should have breadcrumb navigation (if available)', async ({ page }) => {
    await page.goto('/strategy');
    
    // Look for breadcrumb elements
    const breadcrumbs = page.locator('.breadcrumb, .breadcrumbs, nav[aria-label*="breadcrumb"]');
    
    if (await breadcrumbs.count() > 0) {
      await expect(breadcrumbs.first()).toBeVisible();
    }
  });

  test('should maintain consistent navigation across pages', async ({ page }) => {
    const pagesToCheck = ['/playbook', '/prompts', '/strategy'];
    
    for (const pageUrl of pagesToCheck) {
      await page.goto(pageUrl);
      
      // Check if main navigation is present
      const homeLink = page.locator('a[href="/"], a[href*="home"]');
      if (await homeLink.count() > 0) {
        await expect(homeLink.first()).toBeVisible();
      }
      
      // Check for consistent header/footer
      const header = page.locator('header, .header');
      const footer = page.locator('footer, .footer');
      
      if (await header.count() > 0) {
        await expect(header.first()).toBeVisible();
      }
    }
  });
});
